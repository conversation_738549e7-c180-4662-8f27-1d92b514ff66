import CharacterCard from './CharacterCard';

type Character = {
  id: string;
  name: string;
  imageUrl: string;
};

type Faction = {
  id: string;
  name: string;
  description: string;
  characters: Character[];
};

type FactionCharactersProps = {
  faction: Faction;
  onSelectCharacter: (characterId: string) => void;
};

export default function FactionCharacters({ faction, onSelectCharacter }: FactionCharactersProps) {
  return (
    <div className="space-y-8"> {/* Padding for navbar is now handled at the page level */}
      <header className="text-center space-y-4 mb-8">
        <h1 className="text-4xl font-bold text-blue-600">{faction.name}</h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">{faction.description}</p>
      </header>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 mt-8">
        {faction.characters.map((character) => (
          <div key={character.id} className="transform transition-transform hover:-translate-y-1">
            <CharacterCard
              id={character.id}
              name={character.name}
              imageUrl={character.imageUrl}
              onClick={onSelectCharacter}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
